import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import ChatPage from './pages/ChatPage';
import ChatHistoryPage from './pages/ChatHistoryPage';
import AdminIngestPage from './pages/AdminIngestPage';
import LoginPage from './pages/LoginPage';

const App: React.FC = () => {
  const [username, setUsername] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Load user and session data from localStorage on app start
  useEffect(() => {
    const savedUsername = localStorage.getItem('username');
    const savedSessionId = localStorage.getItem('chatSessionId');
    
    if (savedUsername) {
      setUsername(savedUsername);
    }
    
    if (savedSessionId) {
      setSessionId(savedSessionId);
    }
  }, []);

  // Handle login
  const handleLogin = (loginUsername: string) => {
    setUsername(loginUsername);
    localStorage.setItem('username', loginUsername);
  };

  // Handle logout
  const handleLogout = () => {
    setUsername(null);
    setSessionId(null);
    localStorage.removeItem('username');
    localStorage.removeItem('chatSessionId');
  };

  // Save session ID to localStorage when it changes
  const handleSessionIdChange = (newSessionId: string) => {
    setSessionId(newSessionId);
    localStorage.setItem('chatSessionId', newSessionId);
  };

  const handleSessionSelect = (selectedSessionId: string) => {
    setSessionId(selectedSessionId);
    localStorage.setItem('chatSessionId', selectedSessionId);
  };

  // Show login page if user is not logged in
  if (!username) {
    return <LoginPage onLogin={handleLogin} />;
  }

  return (
    <Router>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-teal-50">
        <Navbar username={username} onLogout={handleLogout} />
        <main className="flex-1 p-6" style={{ height: 'calc(100vh - 64px)' }}>
          <Routes>
            <Route 
              path="/" 
              element={
                <ChatPage 
                  username={username}
                  sessionId={sessionId}
                  onSessionIdChange={handleSessionIdChange}
                />
              } 
            />
            <Route 
              path="/history" 
              element={
                <ChatHistoryPage 
                  username={username}
                  onSessionSelect={handleSessionSelect}
                />
              } 
            />
            <Route path="/admin" element={<AdminIngestPage />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
};

export default App;