import React, { useState } from 'react';
import { Globe, Plus, X, CheckCircle, AlertCircle } from 'lucide-react';

interface ConfluenceIngestFormProps {
  onIngest: (pageIds: string[], includeChildren: boolean) => Promise<void>;
}

const ConfluenceIngestForm: React.FC<ConfluenceIngestFormProps> = ({ onIngest }) => {
  const [pageIds, setPageIds] = useState<string[]>(['']);
  const [includeChildren, setIncludeChildren] = useState(true);
  const [isIngesting, setIsIngesting] = useState(false);
  const [ingestStatus, setIngestStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [ingestMessage, setIngestMessage] = useState('');

  const addPageIdField = () => {
    setPageIds([...pageIds, '']);
  };

  const removePageIdField = (index: number) => {
    setPageIds(pageIds.filter((_, i) => i !== index));
  };

  const updatePageId = (index: number, value: string) => {
    const newPageIds = [...pageIds];
    newPageIds[index] = value;
    setPageIds(newPageIds);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validPageIds = pageIds.filter(id => id.trim() !== '');
    if (validPageIds.length === 0) {
      setIngestStatus('error');
      setIngestMessage('Please enter at least one page ID');
      return;
    }

    setIsIngesting(true);
    setIngestStatus('idle');
    setIngestMessage('');

    try {
      await onIngest(validPageIds, includeChildren);
      setIngestStatus('success');
      setIngestMessage(`Successfully ingested ${validPageIds.length} Confluence page(s)`);
      setPageIds(['']);
    } catch (error) {
      setIngestStatus('error');
      setIngestMessage(`Failed to ingest Confluence pages: ${error}`);
    } finally {
      setIsIngesting(false);
    }
  };

  const clearStatus = () => {
    setIngestStatus('idle');
    setIngestMessage('');
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-4">
        <Globe className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">Confluence Integration</h3>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Confluence Page IDs
          </label>
          <div className="space-y-2">
            {pageIds.map((pageId, index) => (
              <div key={index} className="flex items-center space-x-2">
                <input
                  type="text"
                  value={pageId}
                  onChange={(e) => updatePageId(index, e.target.value)}
                  placeholder="Enter Confluence page ID"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
                {pageIds.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removePageIdField(index)}
                    className="p-2 text-gray-400 hover:text-red-500"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            ))}
          </div>
          <button
            type="button"
            onClick={addPageIdField}
            className="mt-2 flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-500"
          >
            <Plus className="h-4 w-4" />
            <span>Add another page ID</span>
          </button>
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="includeChildren"
            checked={includeChildren}
            onChange={(e) => setIncludeChildren(e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="includeChildren" className="text-sm text-gray-700">
            Include child pages
          </label>
        </div>

        <button
          type="submit"
          disabled={isIngesting}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isIngesting ? 'Ingesting...' : 'Ingest Confluence Pages'}
        </button>
      </form>

      {ingestMessage && (
        <div className={`mt-4 flex items-center justify-between p-3 rounded-lg ${
          ingestStatus === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
        }`}>
          <div className="flex items-center space-x-2">
            {ingestStatus === 'success' ? (
              <CheckCircle className="h-5 w-5" />
            ) : (
              <AlertCircle className="h-5 w-5" />
            )}
            <span className="text-sm">{ingestMessage}</span>
          </div>
          <button
            onClick={clearStatus}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
};

export default ConfluenceIngestForm;