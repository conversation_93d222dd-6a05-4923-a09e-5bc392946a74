import React, { useState, useEffect } from 'react';
import { Message } from '../types';
import { User, Bo<PERSON>, ChevronRight } from 'lucide-react';

interface InteractiveMessageProps {
  message: Message;
  onStepClick?: (stepText: string) => void;
  isWaitingForResponse?: boolean;
  selectedStep?: string;
}

const InteractiveMessage: React.FC<InteractiveMessageProps> = ({ 
  message, 
  onStepClick, 
  isWaitingForResponse = false,
  selectedStep 
}) => {
  const [processedContent, setProcessedContent] = useState<string>('');

  useEffect(() => {
    if (!message.isUser && onStepClick) {
      // Process the HTML content to make steps clickable
      const processed = processStepsInContent(message.content, onStepClick, isWaitingForResponse, selectedStep);
      setProcessedContent(processed);
    } else {
      setProcessedContent(message.content);
    }
  }, [message.content, onStepClick, isWaitingForResponse, selectedStep]);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const processStepsInContent = (
    content: string, 
    onStepClick: (stepText: string) => void,
    isWaiting: boolean,
    selectedStep?: string
  ): string => {
    // Create a temporary div to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;

    // Find all list items that look like steps
    const listItems = tempDiv.querySelectorAll('li, ol li, ul li');
    
    listItems.forEach((li, index) => {
      const text = li.textContent || '';
      
      // Check if this looks like a step (contains "step" or is numbered)
      const isStep = /step\s*\d+/i.test(text) || 
                    /^\d+[\.\)]\s/.test(text) || 
                    /^(first|second|third|fourth|fifth|next|then|finally)/i.test(text);
      
      if (isStep && text.trim().length > 0) {
        const stepText = text.trim();
        const isSelected = selectedStep === stepText;
        const isDisabled = isWaiting && !isSelected;
        
        // Create clickable step element
        const stepElement = document.createElement('div');
        stepElement.className = `interactive-step ${isSelected ? 'selected' : ''} ${isDisabled ? 'disabled' : ''}`;
        stepElement.innerHTML = `
          <div class="step-content">
            <span class="step-text">${li.innerHTML}</span>
            <svg class="step-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9,18 15,12 9,6"></polyline>
            </svg>
          </div>
        `;
        
        // Add click handler
        if (!isDisabled) {
          stepElement.style.cursor = 'pointer';
          stepElement.addEventListener('click', () => {
            onStepClick(stepText);
          });
        }
        
        // Replace the original li with our interactive element
        li.parentNode?.replaceChild(stepElement, li);
      }
    });

    return tempDiv.innerHTML;
  };

  return (
    <div className={`flex items-start space-x-3 ${message.isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
      <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
        message.isUser ? 'bg-blue-600' : 'bg-gray-600'
      }`}>
        {message.isUser ? (
          <User className="h-4 w-4 text-white" />
        ) : (
          <Bot className="h-4 w-4 text-white" />
        )}
      </div>
      
      <div className={`flex flex-col max-w-xs lg:max-w-md xl:max-w-lg ${message.isUser ? 'items-end' : 'items-start'}`}>
        <div className={`px-4 py-2 rounded-2xl ${
          message.isUser
            ? 'bg-blue-600 text-white rounded-br-md'
            : 'bg-gray-100 text-gray-900 rounded-bl-md'
        }`}>
          {message.isUser ? (
            <p className="text-sm">{message.content}</p>
          ) : (
            <div 
              className="text-sm prose prose-sm max-w-none interactive-message"
              dangerouslySetInnerHTML={{ __html: processedContent }}
            />
          )}
        </div>
        <span className="text-xs text-gray-500 mt-1 px-1">
          {formatTime(message.timestamp)}
        </span>
      </div>
    </div>
  );
};

export default InteractiveMessage;