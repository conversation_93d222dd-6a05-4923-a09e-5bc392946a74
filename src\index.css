@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.5;
  }
}

@layer components {
  /* Custom scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Prose styles for bot messages */
  .prose ul {
    list-style-type: disc;
    padding-left: 1.5rem;
  }
  
  .prose ol {
    list-style-type: decimal;
    padding-left: 1.5rem;
  }
  
  .prose li {
    margin: 0.25rem 0;
  }
  
  .prose h1, .prose h2, .prose h3, .prose h4 {
    font-weight: 600;
    margin: 1rem 0 0.5rem 0;
  }
  
  .prose h1 {
    font-size: 1.25rem;
  }
  
  .prose h2 {
    font-size: 1.125rem;
  }
  
  .prose h3 {
    font-size: 1rem;
  }
  
  .prose p {
    margin: 0.5rem 0;
  }
  
  .prose a {
    color: #3b82f6;
    text-decoration: underline;
  }
  
  .prose strong {
    font-weight: 600;
  }
  
  .prose code {
    background-color: #f1f5f9;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }

  /* Interactive step styles */
  .interactive-step {
    @apply my-2 p-3 border border-gray-200 rounded-lg transition-all duration-200 hover:border-blue-300 hover:bg-blue-50;
  }

  .interactive-step.selected {
    @apply border-blue-500 bg-blue-100 ring-2 ring-blue-200;
  }

  .interactive-step.disabled {
    @apply opacity-50 cursor-not-allowed;
  }

  .interactive-step:not(.disabled):hover {
    @apply shadow-sm;
  }

  .step-content {
    @apply flex items-center justify-between;
  }

  .step-text {
    @apply flex-1 text-sm font-medium text-gray-800;
  }

  .step-icon {
    @apply ml-2 text-gray-400 transition-colors;
  }

  .interactive-step:not(.disabled):hover .step-icon {
    @apply text-blue-500;
  }

  .interactive-step.selected .step-icon {
    @apply text-blue-600;
  }

  /* Remove default list styling for interactive steps */
  .interactive-message .interactive-step {
    list-style: none;
    margin-left: 0;
    padding-left: 0;
  }
}

@layer utilities {
  .glass {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}