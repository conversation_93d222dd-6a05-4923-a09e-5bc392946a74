import React, { useState } from 'react';
import { Settings, FileText, Globe, Activity } from 'lucide-react';
import FileUploader from '../components/FileUploader';
import ConfluenceIngestForm from '../components/ConfluenceIngestForm';
import { api } from '../utils/api';

interface IngestLog {
  id: string;
  timestamp: Date;
  type: 'file' | 'confluence';
  message: string;
  success: boolean;
}

const AdminIngestPage: React.FC = () => {
  const [logs, setLogs] = useState<IngestLog[]>([]);

  const addLog = (type: 'file' | 'confluence', message: string, success: boolean) => {
    const newLog: IngestLog = {
      id: Date.now().toString(),
      timestamp: new Date(),
      type,
      message,
      success,
    };
    setLogs(prev => [newLog, ...prev.slice(0, 9)]); // Keep only last 10 logs
  };

  const handleFileUpload = async (file: File) => {
    try {
      const result = await api.ingestFile(file);
      addLog('file', `Successfully ingested ${file.name} - ${result.chunks_count} chunks processed`, true);
    } catch (error) {
      addLog('file', `Failed to ingest ${file.name}: ${error}`, false);
      throw error;
    }
  };

  const handleConfluenceIngest = async (pageIds: string[], includeChildren: boolean) => {
    try {
      const result = await api.ingestConfluence(pageIds, includeChildren);
      addLog('confluence', `Successfully ingested ${pageIds.length} Confluence page(s) - ${result.chunks_count} chunks processed`, true);
    } catch (error) {
      addLog('confluence', `Failed to ingest Confluence pages: ${error}`, false);
      throw error;
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-2">
          <Settings className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">Admin Panel</h1>
        </div>
        <p className="text-gray-600">
          Manage document ingestion and system configuration
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Ingestion Forms */}
        <div className="lg:col-span-2 space-y-8">
          {/* File Upload Section */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-2 mb-4">
              <FileText className="h-5 w-5 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900">Document Upload</h2>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Upload documents to be processed and added to the knowledge base.
            </p>
            <FileUploader onUpload={handleFileUpload} />
          </div>

          {/* Confluence Integration */}
          <ConfluenceIngestForm onIngest={handleConfluenceIngest} />
        </div>

        {/* Activity Logs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Activity className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Activity Log</h3>
          </div>
          
          {logs.length === 0 ? (
            <p className="text-sm text-gray-500 text-center py-8">
              No activity yet. Start by uploading a document or ingesting Confluence pages.
            </p>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {logs.map((log) => (
                <div
                  key={log.id}
                  className={`p-3 rounded-lg border ${
                    log.success
                      ? 'bg-green-50 border-green-200'
                      : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex items-start justify-between mb-1">
                    <div className="flex items-center space-x-2">
                      {log.type === 'file' ? (
                        <FileText className="h-4 w-4 text-gray-600" />
                      ) : (
                        <Globe className="h-4 w-4 text-gray-600" />
                      )}
                      <span className="text-xs font-medium text-gray-600 uppercase">
                        {log.type}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">
                      {formatTime(log.timestamp)}
                    </span>
                  </div>
                  <p className={`text-sm ${
                    log.success ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {log.message}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminIngestPage;