import React, { useState, useEffect, useRef } from 'react';
import { Send, Loader2 } from 'lucide-react';
import InteractiveMessage from '../components/InteractiveMessage';
import { Message } from '../types';
import { api } from '../utils/api';

interface ChatPageProps {
  username: string;
  sessionId: string | null;
  onSessionIdChange: (sessionId: string) => void;
}

const ChatPage: React.FC<ChatPageProps> = ({ username, sessionId, onSessionIdChange }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentStreamingMessage, setCurrentStreamingMessage] = useState('');
  const [selectedStep, setSelectedStep] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, currentStreamingMessage]);

  const sendMessage = async (query: string) => {
    if (isStreaming) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: query,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsStreaming(true);
    setCurrentStreamingMessage('');

    try {
      const response = await api.chatStream(query, sessionId, username);
      const reader = response.body?.getReader();
      
      if (!reader) {
        throw new Error('No reader available');
      }

      let streamingContent = '';
      let extractedSessionId = sessionId;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        
        // Check if this chunk contains a session ID
        if (chunk.includes('[SESSION_ID]:')) {
          const sessionIdMatch = chunk.match(/\[SESSION_ID\]:\s*([a-f0-9-]+)/);
          if (sessionIdMatch) {
            extractedSessionId = sessionIdMatch[1];
            onSessionIdChange(extractedSessionId);
            continue; // Skip this chunk for display
          }
        }

        streamingContent += chunk;
        setCurrentStreamingMessage(streamingContent);
      }

      // Add the complete bot message
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: streamingContent,
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, botMessage]);
      setCurrentStreamingMessage('');

    } catch (error) {
      console.error('Error streaming chat:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: 'Sorry, I encountered an error while processing your request. Please try again.',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
      setCurrentStreamingMessage('');
    } finally {
      setIsStreaming(false);
      setSelectedStep(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;

    const query = inputValue.trim();
    setInputValue('');
    await sendMessage(query);
  };

  const handleStepClick = async (stepText: string) => {
    if (isStreaming) return;
    
    setSelectedStep(stepText);
    
    // Create a query to explain the step
    let query = '';
    if (stepText.toLowerCase().includes('step')) {
      query = `Explain ${stepText}`;
    } else {
      query = `Please explain: ${stepText}`;
    }
    
    await sendMessage(query);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // Show only the latest 15 message pairs (30 messages total)
  const displayMessages = messages.slice(-30);

  return (
    <div className="flex flex-col h-full max-w-4xl mx-auto">
      {/* Welcome Message */}
      {displayMessages.length === 0 && !currentStreamingMessage && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Send className="h-8 w-8 text-blue-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome to QueryMate</h2>
            <p className="text-gray-600">
              Ask me questions about your documents, customers, or any other information that has been ingested into the system.
            </p>
          </div>
        </div>
      )}

      {/* Messages */}
      {(displayMessages.length > 0 || currentStreamingMessage) && (
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {displayMessages.map((message) => (
            <InteractiveMessage 
              key={message.id} 
              message={message} 
              onStepClick={message.isUser ? undefined : handleStepClick}
              isWaitingForResponse={isStreaming}
              selectedStep={selectedStep}
            />
          ))}
          
          {/* Streaming message */}
          {currentStreamingMessage && (
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center">
                <Loader2 className="h-4 w-4 text-white animate-spin" />
              </div>
              <div className="flex flex-col max-w-xs lg:max-w-md xl:max-w-lg">
                <div className="px-4 py-2 bg-gray-100 text-gray-900 rounded-2xl rounded-bl-md">
                  <div 
                    className="text-sm prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: currentStreamingMessage }}
                  />
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
      )}

      {/* Input Form */}
      <div className="border-t border-gray-200 p-4">
        <form onSubmit={handleSubmit} className="flex space-x-3">
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about your documents or customers..."
              className="w-full px-4 py-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 max-h-32"
              rows={1}
              disabled={isStreaming}
            />
          </div>
          <button
            type="submit"
            disabled={!inputValue.trim() || isStreaming}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isStreaming ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <Send className="h-5 w-5" />
            )}
          </button>
        </form>
      </div>
    </div>
  );
};

export default ChatPage;